// UI component styles (buttons, inputs, etc.)

// Button styles
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  white-space: nowrap;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: var(--transition-all);
  cursor: pointer;
  border: none;
  outline: none;

  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  &:focus-visible {
    outline: 2px solid var(--blue-500);
    outline-offset: 2px;
  }

  svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }

  // Button variants
  &.btn-primary {
    background-color: var(--neutral-900);
    color: var(--neutral-100);
    box-shadow: var(--shadow-sm);

    &:hover:not(:disabled) {
      background-color: rgba(23, 23, 23, 0.9);
    }
  }

  &.btn-secondary {
    background-color: var(--neutral-700);
    color: var(--neutral-100);
    box-shadow: var(--shadow-sm);

    &:hover:not(:disabled) {
      background-color: rgba(64, 64, 64, 0.8);
    }
  }

  &.btn-ghost {
    background: transparent;
    color: inherit;

    &:hover:not(:disabled) {
      background-color: var(--neutral-700);
    }
  }

  &.btn-outline {
    border: 1px solid var(--neutral-600);
    background-color: transparent;
    color: var(--neutral-100);
    box-shadow: var(--shadow-sm);

    &:hover:not(:disabled) {
      background-color: var(--neutral-700);
    }
  }

  // Button sizes
  &.btn-sm {
    height: 2rem;
    border-radius: var(--radius-md);
    gap: 6px;
    padding: 0 var(--spacing-3);

    svg {
      width: 0.875rem;
      height: 0.875rem;
    }
  }

  &.btn-md {
    height: 2.25rem;
    padding: var(--spacing-2) var(--spacing-4);

    svg {
      width: 1rem;
      height: 1rem;
    }
  }

  &.btn-lg {
    height: 2.5rem;
    border-radius: var(--radius-md);
    padding: 0 var(--spacing-6);

    svg {
      width: 1rem;
      height: 1rem;
    }
  }

  &.btn-icon {
    width: 2.25rem;
    height: 2.25rem;
    padding: 0;
  }
}

// Input styles
.input {
  display: flex;
  width: 100%;
  border-radius: var(--radius-md);
  border: 1px solid var(--neutral-600);
  background-color: transparent;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
  color: var(--neutral-100);
  transition: var(--transition-colors);

  &::placeholder {
    color: var(--neutral-500);
  }

  &:focus {
    outline: none;
    border-color: var(--blue-500);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

// Textarea styles
.textarea {
  @extend .input;
  min-height: 2.5rem;
  resize: vertical;
}

// Select styles
.select {
  position: relative;
  display: inline-block;

  &__trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    background-color: transparent;
    border: 1px solid var(--neutral-600);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    color: var(--neutral-100);
    cursor: pointer;
    transition: var(--transition-colors);

    &:hover {
      background-color: var(--neutral-700);
    }

    &:focus {
      outline: none;
      border-color: var(--blue-500);
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    svg {
      width: 1rem;
      height: 1rem;
      opacity: 0.5;
    }
  }

  &__content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: var(--z-dropdown);
    margin-top: 2px;
    background-color: var(--neutral-700);
    border: 1px solid var(--neutral-600);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 200px;
    overflow-y: auto;

    &.hidden {
      display: none;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--text-sm);
    color: var(--neutral-100);
    cursor: pointer;
    transition: var(--transition-colors);

    &:hover {
      background-color: var(--neutral-600);
    }

    &.selected {
      background-color: var(--blue-500);
      color: white;
    }

    svg {
      width: 1rem;
      height: 1rem;
      margin-right: var(--spacing-2);
    }
  }
}

// Card styles
.card {
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-700);
  background-color: var(--neutral-800);
  color: var(--neutral-100);
  box-shadow: var(--shadow-sm);

  &__header {
    padding: var(--spacing-6);
    padding-bottom: 0;
  }

  &__title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    line-height: var(--leading-none);
    margin-bottom: var(--spacing-2);
  }

  &__description {
    font-size: var(--text-sm);
    color: var(--neutral-400);
  }

  &__content {
    padding: var(--spacing-6);
  }

  &__footer {
    padding: var(--spacing-6);
    padding-top: 0;
  }
}

// Badge styles
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: var(--radius-full);
  padding: 2px var(--spacing-2);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: var(--transition-colors);

  &.badge-default {
    background-color: var(--neutral-700);
    color: var(--neutral-100);
  }

  &.badge-secondary {
    background-color: var(--neutral-600);
    color: var(--neutral-100);
  }

  &.badge-outline {
    border: 1px solid var(--neutral-600);
    color: var(--neutral-100);
  }
}
