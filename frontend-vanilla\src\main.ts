import './styles/main.scss';
import { App } from './app';

/**
 * Application entry point
 */
class Application {
  private app?: App;
  private rootElement: HTMLElement;

  constructor() {
    this.rootElement = document.getElementById('root')!;
    
    if (!this.rootElement) {
      throw new Error('Root element not found');
    }

    this.init();
  }

  private init(): void {
    // Add dark class to html element
    document.documentElement.classList.add('dark');
    
    // Create and mount the app
    this.app = new App();
    this.rootElement.appendChild(this.app.render());

    // Setup global error handling
    this.setupErrorHandling();

    // Setup development helpers
    if (import.meta.env.DEV) {
      this.setupDevHelpers();
    }

    console.log('🚀 Vanilla TypeScript Chat App initialized');
  }

  private setupErrorHandling(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      this.handleError(event.error);
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.handleError(event.reason);
    });
  }

  private handleError(error: any): void {
    // In a production app, you might want to send errors to a logging service
    console.error('Application error:', error);
    
    // Show user-friendly error message
    this.showErrorMessage('An unexpected error occurred. Please refresh the page and try again.');
  }

  private showErrorMessage(message: string): void {
    // Create error notification
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: var(--red-500);
      color: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      z-index: 9999;
      max-width: 400px;
    `;
    errorDiv.textContent = message;

    document.body.appendChild(errorDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 5000);
  }

  private setupDevHelpers(): void {
    // Expose app instance to global scope for debugging
    (window as any).__app = this.app;
    
    // Add development styles
    const devStyles = document.createElement('style');
    devStyles.textContent = `
      /* Development helpers */
      .dev-info {
        position: fixed;
        bottom: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
      }
    `;
    document.head.appendChild(devStyles);

    // Add dev info panel
    const devInfo = document.createElement('div');
    devInfo.className = 'dev-info';
    devInfo.innerHTML = `
      <div>🔧 Development Mode</div>
      <div>App available at: window.__app</div>
    `;
    document.body.appendChild(devInfo);

    console.log('🔧 Development helpers enabled');
    console.log('Access app instance via window.__app');
  }

  /**
   * Destroy the application
   */
  destroy(): void {
    if (this.app) {
      this.app.destroy();
      this.app = undefined;
    }
    
    this.rootElement.innerHTML = '';
  }

  /**
   * Get the app instance
   */
  getApp(): App | undefined {
    return this.app;
  }
}

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new Application();
  });
} else {
  new Application();
}

// Hot module replacement for development
if (import.meta.hot) {
  import.meta.hot.accept();
}
