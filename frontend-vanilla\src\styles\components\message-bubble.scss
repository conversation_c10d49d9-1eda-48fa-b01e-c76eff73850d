// Message bubble styles
.message-bubble {
  position: relative;
  word-wrap: break-word;
  border-radius: var(--radius-xl);
  padding: var(--spacing-3);
  box-shadow: var(--shadow-sm);

  &.human {
    color: white;
    background-color: var(--neutral-700);
    border-bottom-right-radius: var(--radius-lg);
    max-width: 100%;

    @include respond-to('sm') {
      max-width: 90%;
    }
  }

  &.ai {
    background-color: var(--neutral-800);
    color: var(--neutral-100);
    border-bottom-left-radius: 0;
    max-width: 85%;
    display: flex;
    flex-direction: column;

    @include respond-to('md') {
      max-width: 80%;
    }

    .activity-section {
      margin-bottom: var(--spacing-3);
      border-bottom: 1px solid var(--neutral-700);
      padding-bottom: var(--spacing-3);
      font-size: var(--text-xs);
    }

    .copy-button {
      align-self: flex-end;
      margin-top: var(--spacing-2);
      cursor: pointer;
      background-color: var(--neutral-700);
      border: 1px solid var(--neutral-600);
      color: var(--neutral-300);
      padding: var(--spacing-2) var(--spacing-3);
      border-radius: var(--radius-md);
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--text-sm);
      transition: var(--transition-colors);

      &:hover {
        background-color: var(--neutral-600);
      }

      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }

  // Markdown content styling within message bubbles
  .markdown-content {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      margin-top: var(--spacing-4);
      margin-bottom: var(--spacing-2);
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      margin-top: var(--spacing-3);
      margin-bottom: var(--spacing-2);
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-bold);
      margin-top: var(--spacing-3);
      margin-bottom: var(--spacing-1);
    }

    p {
      margin-bottom: var(--spacing-3);
      line-height: var(--leading-relaxed);
    }

    a {
      display: inline-flex;
      align-items: center;
      padding: 0 var(--spacing-2);
      margin: 0 2px;
      border-radius: var(--radius-md);
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
      background-color: var(--neutral-700);
      color: var(--blue-400);

      &:hover {
        color: var(--blue-300);
      }
    }

    ul {
      list-style-type: disc;
      padding-left: var(--spacing-6);
      margin-bottom: var(--spacing-3);
    }

    ol {
      list-style-type: decimal;
      padding-left: var(--spacing-6);
      margin-bottom: var(--spacing-3);
    }

    li {
      margin-bottom: var(--spacing-1);
    }

    blockquote {
      border-left: 4px solid var(--neutral-600);
      padding-left: var(--spacing-4);
      font-style: italic;
      margin: var(--spacing-3) 0;
      font-size: var(--text-sm);
    }

    code {
      background-color: var(--neutral-900);
      border-radius: var(--radius);
      padding: 1px var(--spacing-1);
      font-family: var(--font-mono);
      font-size: var(--text-xs);
    }

    pre {
      background-color: var(--neutral-900);
      padding: var(--spacing-3);
      border-radius: var(--radius-lg);
      overflow-x: auto;
      font-family: var(--font-mono);
      font-size: var(--text-xs);
      margin: var(--spacing-3) 0;

      code {
        background: none;
        padding: 0;
      }
    }

    hr {
      border: none;
      border-top: 1px solid var(--neutral-600);
      margin: var(--spacing-4) 0;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin: var(--spacing-3) 0;
      overflow-x: auto;
      display: block;
      white-space: nowrap;

      th,
      td {
        border: 1px solid var(--neutral-600);
        padding: var(--spacing-3);
        text-align: left;
      }

      th {
        font-weight: var(--font-bold);
      }
    }
  }
}
