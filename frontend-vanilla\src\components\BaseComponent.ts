import { EventEmitter } from '@/utils/events';
import { createElement } from '@/utils/dom';
import type { Component } from '@/types';

/**
 * Base component class that all components extend
 */
export abstract class BaseComponent extends EventEmitter implements Component {
  protected _element: HTMLElement;
  protected _mounted = false;
  protected _destroyed = false;

  constructor(tag: keyof HTMLElementTagNameMap = 'div', className?: string) {
    super();
    this._element = createElement(tag, { className });
    this.init();
  }

  /**
   * Get the component's DOM element
   */
  get element(): HTMLElement {
    return this._element;
  }

  /**
   * Check if component is mounted
   */
  get mounted(): boolean {
    return this._mounted;
  }

  /**
   * Check if component is destroyed
   */
  get destroyed(): boolean {
    return this._destroyed;
  }

  /**
   * Initialize the component (called in constructor)
   */
  protected init(): void {
    // Override in subclasses
  }

  /**
   * Render the component and return its element
   */
  render(): HTMLElement {
    if (this._destroyed) {
      throw new Error('Cannot render destroyed component');
    }

    if (!this._mounted) {
      this.mount();
    }

    return this._element;
  }

  /**
   * Mount the component (setup event listeners, etc.)
   */
  protected mount(): void {
    if (this._mounted || this._destroyed) return;

    this.onMount();
    this._mounted = true;
    this.emit('mounted');
  }

  /**
   * Unmount the component (cleanup event listeners, etc.)
   */
  protected unmount(): void {
    if (!this._mounted || this._destroyed) return;

    this.onUnmount();
    this._mounted = false;
    this.emit('unmounted');
  }

  /**
   * Destroy the component and cleanup resources
   */
  destroy(): void {
    if (this._destroyed) return;

    this.unmount();
    this.onDestroy();
    this.removeAllListeners();
    
    if (this._element.parentNode) {
      this._element.parentNode.removeChild(this._element);
    }

    this._destroyed = true;
    this.emit('destroyed');
  }

  /**
   * Update the component with new data
   */
  update(data?: any): void {
    if (this._destroyed) return;
    this.onUpdate(data);
    this.emit('updated', data);
  }

  /**
   * Show the component
   */
  show(): void {
    this._element.style.display = '';
    this.emit('shown');
  }

  /**
   * Hide the component
   */
  hide(): void {
    this._element.style.display = 'none';
    this.emit('hidden');
  }

  /**
   * Toggle component visibility
   */
  toggle(force?: boolean): boolean {
    const isHidden = this._element.style.display === 'none';
    const shouldShow = force !== undefined ? force : isHidden;
    
    if (shouldShow) {
      this.show();
    } else {
      this.hide();
    }
    
    return shouldShow;
  }

  /**
   * Add CSS class to component element
   */
  addClass(...classes: string[]): void {
    this._element.classList.add(...classes);
  }

  /**
   * Remove CSS class from component element
   */
  removeClass(...classes: string[]): void {
    this._element.classList.remove(...classes);
  }

  /**
   * Toggle CSS class on component element
   */
  toggleClass(className: string, force?: boolean): boolean {
    return this._element.classList.toggle(className, force);
  }

  /**
   * Check if component element has CSS class
   */
  hasClass(className: string): boolean {
    return this._element.classList.contains(className);
  }

  /**
   * Set attribute on component element
   */
  setAttribute(name: string, value: string): void {
    this._element.setAttribute(name, value);
  }

  /**
   * Get attribute from component element
   */
  getAttribute(name: string): string | null {
    return this._element.getAttribute(name);
  }

  /**
   * Remove attribute from component element
   */
  removeAttribute(name: string): void {
    this._element.removeAttribute(name);
  }

  /**
   * Find child element by selector
   */
  protected query<T extends Element = Element>(selector: string): T | null {
    return this._element.querySelector<T>(selector);
  }

  /**
   * Find all child elements by selector
   */
  protected queryAll<T extends Element = Element>(selector: string): NodeListOf<T> {
    return this._element.querySelectorAll<T>(selector);
  }

  // Lifecycle hooks (override in subclasses)
  protected onMount(): void {}
  protected onUnmount(): void {}
  protected onDestroy(): void {}
  protected onUpdate(_data?: any): void {}
}
