// Import base styles
@import 'base/variables';
@import 'base/reset';

// Import utility styles
@import 'utilities/animations';
@import 'utilities/layout';
@import 'utilities/typography';

// Import component styles
@import 'components/app';
@import 'components/welcome-screen';
@import 'components/chat-interface';
@import 'components/input-form';
@import 'components/activity-timeline';
@import 'components/message-bubble';
@import 'components/ui';

// Global application styles
.app {
  display: flex;
  height: 100vh;
  background-color: var(--neutral-800);
  color: var(--neutral-100);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 64rem; // max-w-4xl
  margin: 0 auto;
  width: 100%;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  
  &.welcome-mode {
    display: flex;
  }
}

// Loading states
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.spinner {
  @include loading-spinner;
}

// Utility classes
.sr-only {
  @include visually-hidden;
}

.flex-center {
  @include flex-center;
}

.fade-in {
  @include fade-in;
}

.fade-in-up {
  @include fade-in-up;
}

// Responsive utilities
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  
  @include respond-to('sm') {
    max-width: 640px;
  }
  
  @include respond-to('md') {
    max-width: 768px;
    padding: 0 var(--spacing-6);
  }
  
  @include respond-to('lg') {
    max-width: 1024px;
  }
  
  @include respond-to('xl') {
    max-width: 1280px;
  }
}

// Dark theme (default)
.dark {
  color-scheme: dark;
}

// Error states
.error {
  color: var(--red-400);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid var(--red-500);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-2) 0;
}

// Success states
.success {
  color: var(--blue-400);
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid var(--blue-500);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-2) 0;
}
