import { BaseComponent } from '../BaseComponent';
import { createElement } from '@/utils/dom';
import { cn } from '@/utils/helpers';

export interface ButtonOptions {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  children?: string | HTMLElement[];
  onClick?: (event: MouseEvent) => void;
}

/**
 * Button component
 */
export class Button extends BaseComponent {
  private options: ButtonOptions;
  private clickHandler?: (event: MouseEvent) => void;

  constructor(options: ButtonOptions = {}) {
    const {
      variant = 'primary',
      size = 'md',
      className = '',
      ...rest
    } = options;

    const buttonClasses = cn(
      'btn',
      `btn-${variant}`,
      `btn-${size}`,
      className
    );

    super('button', buttonClasses);
    this.options = { variant, size, className, ...rest };
    this.setupButton();
  }

  private setupButton(): void {
    const button = this._element as HTMLButtonElement;
    
    // Set button type
    button.type = this.options.type || 'button';
    
    // Set disabled state
    if (this.options.disabled) {
      button.disabled = true;
    }

    // Set content
    if (this.options.children) {
      this.setContent(this.options.children);
    }

    // Set click handler
    if (this.options.onClick) {
      this.clickHandler = this.options.onClick;
    }
  }

  protected onMount(): void {
    if (this.clickHandler) {
      this._element.addEventListener('click', this.clickHandler);
    }
  }

  protected onUnmount(): void {
    if (this.clickHandler) {
      this._element.removeEventListener('click', this.clickHandler);
    }
  }

  /**
   * Set button content
   */
  setContent(content: string | HTMLElement[]): void {
    this._element.innerHTML = '';
    
    if (typeof content === 'string') {
      this._element.textContent = content;
    } else {
      content.forEach(element => {
        this._element.appendChild(element);
      });
    }
  }

  /**
   * Set disabled state
   */
  setDisabled(disabled: boolean): void {
    const button = this._element as HTMLButtonElement;
    button.disabled = disabled;
    this.options.disabled = disabled;
  }

  /**
   * Get disabled state
   */
  isDisabled(): boolean {
    return (this._element as HTMLButtonElement).disabled;
  }

  /**
   * Set loading state
   */
  setLoading(loading: boolean): void {
    if (loading) {
      this.addClass('loading');
      this.setDisabled(true);
      
      // Add spinner if not already present
      const spinner = this.query('.spinner');
      if (!spinner) {
        const spinnerElement = createElement('div', {
          className: 'spinner animate-spin',
          innerHTML: '⟳'
        });
        this._element.insertBefore(spinnerElement, this._element.firstChild);
      }
    } else {
      this.removeClass('loading');
      this.setDisabled(this.options.disabled || false);
      
      // Remove spinner
      const spinner = this.query('.spinner');
      if (spinner) {
        spinner.remove();
      }
    }
  }

  /**
   * Update button options
   */
  updateOptions(newOptions: Partial<ButtonOptions>): void {
    this.options = { ...this.options, ...newOptions };
    
    // Update classes
    this._element.className = cn(
      'btn',
      `btn-${this.options.variant}`,
      `btn-${this.options.size}`,
      this.options.className
    );

    // Update properties
    if (newOptions.disabled !== undefined) {
      this.setDisabled(newOptions.disabled);
    }

    if (newOptions.children !== undefined) {
      this.setContent(newOptions.children);
    }

    if (newOptions.onClick !== undefined) {
      // Remove old handler
      if (this.clickHandler) {
        this._element.removeEventListener('click', this.clickHandler);
      }
      
      // Set new handler
      this.clickHandler = newOptions.onClick;
      if (this._mounted && this.clickHandler) {
        this._element.addEventListener('click', this.clickHandler);
      }
    }
  }

  /**
   * Trigger click programmatically
   */
  click(): void {
    (this._element as HTMLButtonElement).click();
  }

  /**
   * Static factory method for creating buttons with icons
   */
  static withIcon(
    iconHtml: string,
    text: string,
    options: ButtonOptions = {}
  ): Button {
    const iconElement = createElement('span', { innerHTML: iconHtml });
    const textElement = createElement('span', { textContent: text });
    
    return new Button({
      ...options,
      children: [iconElement, textElement]
    });
  }

  /**
   * Static factory method for creating icon-only buttons
   */
  static iconOnly(iconHtml: string, options: ButtonOptions = {}): Button {
    const iconElement = createElement('span', { innerHTML: iconHtml });
    
    return new Button({
      ...options,
      size: 'icon',
      children: [iconElement]
    });
  }
}
