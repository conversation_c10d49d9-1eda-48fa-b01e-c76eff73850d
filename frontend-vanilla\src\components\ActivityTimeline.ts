import { BaseComponent } from './BaseComponent';
import { createElement } from '@/utils/dom';
import type { ProcessedEvent } from '@/types';

export interface ActivityTimelineOptions {
  processedEvents: ProcessedEvent[];
  isLoading: boolean;
}

/**
 * Activity timeline component - shows research progress
 */
export class ActivityTimeline extends BaseComponent {
  private options: ActivityTimelineOptions;
  private isCollapsed = false;
  private headerElement!: HTMLElement;
  private contentElement!: HTMLElement;
  private eventsContainer!: HTMLElement;

  constructor(options: ActivityTimelineOptions) {
    super('div', 'activity-timeline');
    this.options = options;
    this.createElements();
    this.updateContent();
  }

  private createElements(): void {
    // Create header
    this.headerElement = createElement('div', {
      className: 'activity-timeline__header'
    });

    const titleElement = createElement('div', {
      className: 'activity-timeline__header__title'
    });

    titleElement.innerHTML = `
      Research
      ${this.isCollapsed ? this.getIconSvg('chevron-down') : this.getIconSvg('chevron-up')}
    `;

    titleElement.addEventListener('click', () => this.toggleCollapsed());

    this.headerElement.appendChild(titleElement);

    // Create content container
    this.contentElement = createElement('div', {
      className: 'activity-timeline__content'
    });

    // Create events container
    this.eventsContainer = createElement('div', {
      className: 'activity-timeline__events'
    });

    this.contentElement.appendChild(this.eventsContainer);

    // Append elements
    this._element.appendChild(this.headerElement);
    this._element.appendChild(this.contentElement);

    // Set initial collapsed state
    if (this.isCollapsed) {
      this.contentElement.style.display = 'none';
    }
  }

  private updateContent(): void {
    this.eventsContainer.innerHTML = '';

    // Show loading state if loading and no events
    if (this.options.isLoading && this.options.processedEvents.length === 0) {
      this.createLoadingState();
      return;
    }

    // Show events if any exist
    if (this.options.processedEvents.length > 0) {
      this.createEventsTimeline();
      
      // Add final loading state if still loading
      if (this.options.isLoading) {
        this.createFinalLoadingState();
      }
    } else if (!this.options.isLoading) {
      // Show empty state
      this.createEmptyState();
    }
  }

  private createLoadingState(): void {
    const loadingElement = createElement('div', {
      className: 'activity-timeline__loading-initial'
    });

    loadingElement.innerHTML = `
      <div class="timeline-line"></div>
      <div class="timeline-dot">
        ${this.getIconSvg('loader', 'spinning')}
      </div>
      <div class="timeline-content">
        <p>Searching...</p>
      </div>
    `;

    this.eventsContainer.appendChild(loadingElement);
  }

  private createEventsTimeline(): void {
    this.options.processedEvents.forEach((event, index) => {
      const eventElement = createElement('div', {
        className: 'activity-timeline__event'
      });

      const isLast = index === this.options.processedEvents.length - 1;
      const showLine = !isLast || (isLast && this.options.isLoading);

      eventElement.innerHTML = `
        ${showLine ? '<div class="timeline-line"></div>' : ''}
        <div class="timeline-dot">
          ${this.getEventIcon(event.title)}
        </div>
        <div class="timeline-content">
          <div class="event-title">${event.title}</div>
          <div class="event-data">${this.formatEventData(event.data)}</div>
        </div>
      `;

      this.eventsContainer.appendChild(eventElement);
    });
  }

  private createFinalLoadingState(): void {
    const loadingElement = createElement('div', {
      className: 'activity-timeline__loading-final'
    });

    loadingElement.innerHTML = `
      <div class="timeline-dot">
        ${this.getIconSvg('loader', 'spinning')}
      </div>
      <div class="timeline-content">
        <p>Searching...</p>
      </div>
    `;

    this.eventsContainer.appendChild(loadingElement);
  }

  private createEmptyState(): void {
    const emptyElement = createElement('div', {
      className: 'activity-timeline__empty-state'
    });

    emptyElement.innerHTML = `
      ${this.getIconSvg('info')}
      <p>No activity to display.</p>
      <p class="subtitle">Timeline will update during processing.</p>
    `;

    this.eventsContainer.appendChild(emptyElement);
  }

  private getEventIcon(title: string): string {
    const lowerTitle = title.toLowerCase();
    
    if (lowerTitle.includes('generating')) {
      return this.getIconSvg('text-search');
    } else if (lowerTitle.includes('thinking')) {
      return this.getIconSvg('loader', 'spinning');
    } else if (lowerTitle.includes('reflection')) {
      return this.getIconSvg('brain');
    } else if (lowerTitle.includes('research')) {
      return this.getIconSvg('search');
    } else if (lowerTitle.includes('finalizing')) {
      return this.getIconSvg('pen');
    }
    
    return this.getIconSvg('activity');
  }

  private formatEventData(data: any): string {
    if (typeof data === 'string') {
      return data;
    } else if (Array.isArray(data)) {
      return data.join(', ');
    } else {
      return JSON.stringify(data);
    }
  }

  private toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
    
    if (this.isCollapsed) {
      this.contentElement.style.display = 'none';
    } else {
      this.contentElement.style.display = '';
    }

    // Update header icon
    const titleElement = this.query('.activity-timeline__header__title');
    if (titleElement) {
      titleElement.innerHTML = `
        Research
        ${this.isCollapsed ? this.getIconSvg('chevron-down') : this.getIconSvg('chevron-up')}
      `;
    }

    this.emit('toggle', { collapsed: this.isCollapsed });
  }

  private getIconSvg(name: string, className = ''): string {
    const icons: Record<string, string> = {
      'chevron-down': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>`,
      'chevron-up': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>`,
      'loader': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>`,
      'info': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4m0-4h.01"></path></svg>`,
      'text-search': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 21h7a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v11m0 5l4.879-4.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242z"></path></svg>`,
      'brain': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>`,
      'search': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="11" cy="11" r="8"></circle><path d="M21 21l-4.35-4.35"></path></svg>`,
      'pen': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle></svg>`,
      'activity': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline></svg>`
    };
    return icons[name] || '';
  }

  /**
   * Update the timeline with new events
   */
  updateEvents(events: ProcessedEvent[], isLoading: boolean): void {
    this.options.processedEvents = events;
    this.options.isLoading = isLoading;
    this.updateContent();

    // Auto-collapse when loading is complete and events exist
    if (!isLoading && events.length > 0 && !this.isCollapsed) {
      setTimeout(() => {
        this.isCollapsed = true;
        this.toggleCollapsed();
      }, 1000);
    }
  }

  /**
   * Set collapsed state
   */
  setCollapsed(collapsed: boolean): void {
    if (this.isCollapsed !== collapsed) {
      this.toggleCollapsed();
    }
  }

  /**
   * Get collapsed state
   */
  getCollapsed(): boolean {
    return this.isCollapsed;
  }
}
