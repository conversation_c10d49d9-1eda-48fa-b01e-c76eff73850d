{"hash": "4e8c9447", "configHash": "24610f45", "lockfileHash": "21acb28c", "browserHash": "2c5c100c", "optimized": {"@angular/common": {"src": "../../../../../../node_modules/.pnpm/@angular+common@20.0.2_@ang_171ce1cc48378a2a695a85431fdcdc6d/node_modules/@angular/common/fesm2022/common.mjs", "file": "@angular_common.js", "fileHash": "d504521a", "needsInterop": false}, "@angular/core": {"src": "../../../../../../node_modules/.pnpm/@angular+core@20.0.2_@angular+compiler@20.0.2_rxjs@7.8.2/node_modules/@angular/core/fesm2022/core.mjs", "file": "@angular_core.js", "fileHash": "f593faa6", "needsInterop": false}, "@angular/forms": {"src": "../../../../../../node_modules/.pnpm/@angular+forms@20.0.2_@angu_a7b68304019e4e6f985bf6cd3bdc0c49/node_modules/@angular/forms/fesm2022/forms.mjs", "file": "@angular_forms.js", "fileHash": "5db03f25", "needsInterop": false}, "@angular/platform-browser": {"src": "../../../../../../node_modules/.pnpm/@angular+platform-browser@2_5a8a0bc9783e8b4c4b1d9d4e2f7c76af/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs", "file": "@angular_platform-browser.js", "fileHash": "c383b63e", "needsInterop": false}, "@angular/router": {"src": "../../../../../../node_modules/.pnpm/@angular+router@20.0.2_@ang_21dece4c0c11a73d993e18671da18acc/node_modules/@angular/router/fesm2022/router.mjs", "file": "@angular_router.js", "fileHash": "97fc788f", "needsInterop": false}, "class-variance-authority": {"src": "../../../../../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "4374e176", "needsInterop": false}, "clsx": {"src": "../../../../../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "8cb6ff90", "needsInterop": false}, "marked": {"src": "../../../../../../node_modules/.pnpm/marked@15.0.12/node_modules/marked/lib/marked.esm.js", "file": "marked.js", "fileHash": "4e4af0ad", "needsInterop": false}, "rxjs": {"src": "../../../../../../node_modules/.pnpm/rxjs@7.8.2/node_modules/rxjs/dist/esm5/index.js", "file": "rxjs.js", "fileHash": "468fff62", "needsInterop": false}, "tailwind-merge": {"src": "../../../../../../node_modules/.pnpm/tailwind-merge@3.3.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "88cc0dea", "needsInterop": false}}, "chunks": {"chunk-FO3S4ZL5": {"file": "chunk-FO3S4ZL5.js"}, "chunk-N6TMTPPI": {"file": "chunk-N6TMTPPI.js"}, "chunk-Q4W42OGO": {"file": "chunk-Q4W42OGO.js"}, "chunk-BOL3DIUT": {"file": "chunk-BOL3DIUT.js"}, "chunk-ODWC25EG": {"file": "chunk-ODWC25EG.js"}, "chunk-GOMI4DH3": {"file": "chunk-GOMI4DH3.js"}}}