import { BaseComponent } from './BaseComponent';
import { Button } from './ui/Button';
import { Select } from './ui/Select';
import { createElement } from '@/utils/dom';
import { cn } from '@/utils/helpers';
import type { FormData } from '@/types';

export interface InputFormOptions {
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
  isLoading: boolean;
  hasHistory: boolean;
}

/**
 * Input form component with textarea, effort selector, model selector, and submit button
 */
export class InputForm extends BaseComponent {
  private options: InputFormOptions;
  private textareaElement!: HTMLTextAreaElement;
  private effortSelect!: Select;
  private modelSelect!: Select;
  private submitButton!: Button;
  private cancelButton!: Button;
  private newSearchButton!: Button;
  private inputContainer!: HTMLElement;
  private controlsContainer!: HTMLElement;

  private currentValue = '';
  private currentEffort = 'medium';
  private currentModel = 'gemini-2.5-flash-preview-04-17';

  constructor(options: InputFormOptions) {
    super('form', 'input-form');
    this.options = options;
    this.createElements();
    this.setupEventListeners();
  }

  private createElements(): void {
    // Create input container
    this.inputContainer = createElement('div', {
      className: cn(
        'input-form__input-container',
        this.options.hasHistory ? 'has-history' : ''
      )
    });

    // Create textarea
    this.textareaElement = createElement('textarea', {
      className: 'input-form__textarea',
      attributes: {
        placeholder: 'Who won the Euro 2024 and scored the most goals?',
        rows: '1'
      }
    }) as HTMLTextAreaElement;

    // Create button container
    const buttonContainer = createElement('div', {
      className: 'input-form__input-container__button-container'
    });

    // Create submit/cancel button
    this.submitButton = new Button({
      variant: 'ghost',
      className: 'input-form__button submit-button',
      children: [
        createElement('span', { textContent: 'Search' }),
        createElement('span', { innerHTML: this.getIconSvg('send') })
      ],
      onClick: () => this.handleSubmit()
    });

    this.cancelButton = new Button({
      variant: 'ghost',
      className: 'input-form__button cancel-button',
      children: [createElement('span', { innerHTML: this.getIconSvg('stop-circle') })],
      onClick: () => this.options.onCancel()
    });

    buttonContainer.appendChild(this.options.isLoading ? this.cancelButton.render() : this.submitButton.render());

    this.inputContainer.appendChild(this.textareaElement);
    this.inputContainer.appendChild(buttonContainer);

    // Create controls container
    this.controlsContainer = createElement('div', {
      className: 'input-form__controls'
    });

    const leftControls = createElement('div', {
      className: 'input-form__controls__left'
    });

    // Create effort select
    this.effortSelect = new Select({
      options: [
        { value: 'low', label: 'Low' },
        { value: 'medium', label: 'Medium' },
        { value: 'high', label: 'High' }
      ],
      value: this.currentEffort,
      className: 'input-form__select-group',
      onChange: (value) => {
        this.currentEffort = value as 'low' | 'medium' | 'high';
      }
    });

    // Create model select
    this.modelSelect = new Select({
      options: [
        { 
          value: 'gemini-2.0-flash', 
          label: '2.0 Flash',
          icon: this.getIconSvg('zap', 'text-yellow-400')
        },
        { 
          value: 'gemini-2.5-flash-preview-04-17', 
          label: '2.5 Flash',
          icon: this.getIconSvg('zap', 'text-orange-400')
        },
        { 
          value: 'gemini-2.5-pro-preview-05-06', 
          label: '2.5 Pro',
          icon: this.getIconSvg('cpu', 'text-purple-400')
        }
      ],
      value: this.currentModel,
      className: 'input-form__select-group',
      onChange: (value) => {
        this.currentModel = value;
      }
    });

    // Wrap selects with labels
    const effortGroup = this.createSelectGroup('Brain', 'brain', this.effortSelect);
    const modelGroup = this.createSelectGroup('Model', 'cpu', this.modelSelect);

    leftControls.appendChild(effortGroup);
    leftControls.appendChild(modelGroup);

    this.controlsContainer.appendChild(leftControls);

    // Create new search button if has history
    if (this.options.hasHistory) {
      this.newSearchButton = new Button({
        variant: 'secondary',
        className: 'input-form__button new-search-button',
        children: [
          createElement('span', { innerHTML: this.getIconSvg('square-pen') }),
          createElement('span', { textContent: 'New Search' })
        ],
        onClick: () => window.location.reload()
      });

      this.controlsContainer.appendChild(this.newSearchButton.render());
    }

    // Append all elements
    this._element.appendChild(this.inputContainer);
    this._element.appendChild(this.controlsContainer);
  }

  private createSelectGroup(label: string, iconName: string, select: Select): HTMLElement {
    const group = createElement('div', {
      className: 'input-form__select-group'
    });

    const labelElement = createElement('div', {
      className: 'input-form__select-group__label',
      innerHTML: `${this.getIconSvg(iconName)} ${label}`
    });

    group.appendChild(labelElement);
    group.appendChild(select.render());

    return group;
  }

  private setupEventListeners(): void {
    // Handle form submission
    this._element.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleSubmit();
    });

    // Handle textarea input
    this.textareaElement.addEventListener('input', () => {
      this.currentValue = this.textareaElement.value;
      this.updateSubmitButton();
      this.autoResize();
    });

    // Handle Enter key (submit on Enter, new line on Shift+Enter)
    this.textareaElement.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.handleSubmit();
      }
    });
  }

  private handleSubmit(): void {
    if (!this.currentValue.trim() || this.options.isLoading) return;

    const formData: FormData = {
      inputValue: this.currentValue,
      effort: this.currentEffort,
      model: this.currentModel
    };

    this.options.onSubmit(formData);
    this.currentValue = '';
    this.textareaElement.value = '';
    this.updateSubmitButton();
  }

  private updateSubmitButton(): void {
    const isDisabled = !this.currentValue.trim() || this.options.isLoading;
    this.submitButton.setDisabled(isDisabled);
  }

  private autoResize(): void {
    this.textareaElement.style.height = 'auto';
    this.textareaElement.style.height = Math.min(this.textareaElement.scrollHeight, 200) + 'px';
  }

  private getIconSvg(name: string, className = ''): string {
    const icons: Record<string, string> = {
      'send': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>`,
      'stop-circle': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect></svg>`,
      'brain': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>`,
      'cpu': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="4" y="4" width="16" height="16" rx="2"></rect><rect x="9" y="9" width="6" height="6"></rect><path d="M9 1v3m6-3v3M9 20v3m6-3v3M20 9h3m-3 6h3M1 9h3m-3 6h3"></path></svg>`,
      'zap': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"></polygon></svg>`,
      'square-pen': `<svg class="${className}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path></svg>`
    };
    return icons[name] || '';
  }

  /**
   * Set loading state
   */
  setLoading(isLoading: boolean): void {
    this.options.isLoading = isLoading;
    
    // Update button container
    const buttonContainer = this.query('.input-form__input-container__button-container');
    if (buttonContainer) {
      buttonContainer.innerHTML = '';
      buttonContainer.appendChild(isLoading ? this.cancelButton.render() : this.submitButton.render());
    }

    this.updateSubmitButton();
  }

  /**
   * Update form options
   */
  updateOptions(newOptions: Partial<InputFormOptions>): void {
    this.options = { ...this.options, ...newOptions };
    
    if (newOptions.isLoading !== undefined) {
      this.setLoading(newOptions.isLoading);
    }

    if (newOptions.hasHistory !== undefined) {
      this.inputContainer.className = cn(
        'input-form__input-container',
        newOptions.hasHistory ? 'has-history' : ''
      );
    }
  }

  /**
   * Focus the textarea
   */
  focus(): void {
    this.textareaElement.focus();
  }

  /**
   * Reset the form
   */
  reset(): void {
    this.currentValue = '';
    this.textareaElement.value = '';
    this.effortSelect.setValue('medium');
    this.modelSelect.setValue('gemini-2.5-flash-preview-04-17');
    this.updateSubmitButton();
  }

  protected onDestroy(): void {
    if (this.effortSelect) this.effortSelect.destroy();
    if (this.modelSelect) this.modelSelect.destroy();
    if (this.submitButton) this.submitButton.destroy();
    if (this.cancelButton) this.cancelButton.destroy();
    if (this.newSearchButton) this.newSearchButton.destroy();
  }
}
