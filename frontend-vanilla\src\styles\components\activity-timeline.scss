// Activity timeline styles
.activity-timeline {
  border: none;
  border-radius: var(--radius-lg);
  background-color: var(--neutral-700);
  max-height: 24rem; // max-h-96 equivalent

  &__header {
    padding: var(--spacing-4) var(--spacing-4) 0;

    &__title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      gap: var(--spacing-2);
      color: var(--neutral-100);
      font-size: var(--text-sm);
      width: 100%;

      svg {
        width: 1rem;
        height: 1rem;
        margin-right: var(--spacing-2);
      }
    }
  }

  &__content {
    max-height: 24rem;
    overflow-y: auto;
    padding: var(--spacing-4);

    // Custom scrollbar for timeline content
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--neutral-800);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--neutral-600);
      border-radius: var(--radius-full);
    }
  }

  &__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--neutral-500);
    padding-top: 2.5rem; // pt-10 equivalent

    svg {
      width: 1.5rem;
      height: 1.5rem;
      margin-bottom: var(--spacing-3);
    }

    p {
      font-size: var(--text-sm);

      &.subtitle {
        font-size: var(--text-xs);
        color: var(--neutral-600);
        margin-top: var(--spacing-1);
      }
    }
  }

  &__loading-initial {
    position: relative;
    padding-left: var(--spacing-8);
    padding-bottom: var(--spacing-4);

    .timeline-line {
      position: absolute;
      left: var(--spacing-3);
      top: 14px; // top-3.5 equivalent
      height: 100%;
      width: 2px;
      background-color: var(--neutral-800);
    }

    .timeline-dot {
      position: absolute;
      left: 2px; // left-0.5 equivalent
      top: var(--spacing-2);
      height: 1.25rem;
      width: 1.25rem;
      border-radius: var(--radius-full);
      background-color: var(--neutral-800);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 0 4px var(--neutral-900);

      svg {
        width: 0.75rem;
        height: 0.75rem;
        color: var(--neutral-400);
        @include loading-spinner;
      }
    }

    .timeline-content {
      p {
        font-size: var(--text-sm);
        color: var(--neutral-300);
        font-weight: var(--font-medium);
      }
    }
  }

  &__events {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  &__event {
    position: relative;
    padding-left: var(--spacing-8);
    padding-bottom: var(--spacing-4);

    .timeline-line {
      position: absolute;
      left: var(--spacing-3);
      top: 14px; // top-3.5 equivalent
      height: 100%;
      width: 2px;
      background-color: var(--neutral-600);

      &.last {
        display: none;
      }
    }

    .timeline-dot {
      position: absolute;
      left: 2px; // left-0.5 equivalent
      top: var(--spacing-2);
      height: 1.5rem;
      width: 1.5rem;
      border-radius: var(--radius-full);
      background-color: var(--neutral-600);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 0 4px var(--neutral-700);

      svg {
        width: 1rem;
        height: 1rem;
        color: var(--neutral-400);

        &.spinning {
          @include loading-spinner;
        }
      }
    }

    .timeline-content {
      .event-title {
        font-size: var(--text-sm);
        color: var(--neutral-200);
        font-weight: var(--font-medium);
        margin-bottom: 2px; // mb-0.5 equivalent
      }

      .event-data {
        font-size: var(--text-xs);
        color: var(--neutral-300);
        line-height: var(--leading-relaxed);
      }
    }
  }

  &__loading-final {
    position: relative;
    padding-left: var(--spacing-8);
    padding-bottom: var(--spacing-4);

    .timeline-dot {
      position: absolute;
      left: 2px; // left-0.5 equivalent
      top: var(--spacing-2);
      height: 1.25rem;
      width: 1.25rem;
      border-radius: var(--radius-full);
      background-color: var(--neutral-600);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 0 4px var(--neutral-700);

      svg {
        width: 0.75rem;
        height: 0.75rem;
        color: var(--neutral-400);
        @include loading-spinner;
      }
    }

    .timeline-content {
      p {
        font-size: var(--text-sm);
        color: var(--neutral-300);
        font-weight: var(--font-medium);
      }
    }
  }
}
