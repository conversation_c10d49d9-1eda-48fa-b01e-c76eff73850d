// Input form styles
.input-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  padding: var(--spacing-3);

  &__input-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    color: white;
    border-radius: var(--radius-3xl);
    border-bottom-left-radius: var(--radius-sm);
    word-wrap: break-word;
    min-height: 1.75rem; // min-h-7 equivalent
    background-color: var(--neutral-700);
    padding: var(--spacing-4);
    padding-top: var(--spacing-3);

    &.has-history {
      border-bottom-right-radius: var(--radius-sm);
    }

    textarea {
      width: 100%;
      color: var(--neutral-100);
      resize: none;
      border: 0;
      outline: none;
      background: transparent;
      font-size: var(--text-base);
      min-height: 56px;
      max-height: 200px;

      @include respond-to('md') {
        font-size: var(--text-base);
      }

      &::placeholder {
        color: var(--neutral-500);
      }

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    &__button-container {
      margin-top: -var(--spacing-3);
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__left {
      display: flex;
      flex-direction: row;
      gap: var(--spacing-2);
    }
  }

  &__select-group {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-2);
    background-color: var(--neutral-700);
    border: 1px solid var(--neutral-600);
    color: var(--neutral-300);
    border-radius: var(--radius-xl);
    border-top-radius: var(--radius-sm);
    padding-left: var(--spacing-2);
    max-width: 100%;

    @include respond-to('sm') {
      max-width: 90%;
    }

    &__label {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: var(--text-sm);

      svg {
        width: 1rem;
        height: 1rem;
        margin-right: var(--spacing-2);
      }
    }

    &__select {
      width: 120px;
      background: transparent;
      border: none;
      cursor: pointer;

      &.model-select {
        width: 150px;
      }
    }
  }
}

// Button styles for the input form
.input-form__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  border-radius: var(--radius-full);
  transition: var(--transition-all);
  cursor: pointer;
  font-size: var(--text-base);

  &.submit-button {
    &:not(:disabled) {
      color: var(--blue-500);

      &:hover {
        color: var(--blue-400);
        background-color: rgba(59, 130, 246, 0.1);
      }
    }

    &:disabled {
      color: var(--neutral-500);
      cursor: not-allowed;
    }
  }

  &.cancel-button {
    color: var(--red-500);

    &:hover {
      color: var(--red-400);
      background-color: rgba(239, 68, 68, 0.1);
    }
  }

  &.new-search-button {
    background-color: var(--neutral-700);
    border: 1px solid var(--neutral-600);
    color: var(--neutral-300);
    border-radius: var(--radius-xl);
    border-top-radius: var(--radius-sm);
    padding-left: var(--spacing-2);

    &:hover {
      background-color: var(--neutral-600);
    }

    svg {
      width: 1rem;
      height: 1rem;
    }
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}
