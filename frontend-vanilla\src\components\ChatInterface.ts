import { BaseComponent } from './BaseComponent';
import { InputForm } from './InputForm';
import { MessageBubble } from './MessageBubble';
import { ActivityTimeline } from './ActivityTimeline';
import { createElement, scrollToBottom } from '@/utils/dom';
import type { Message, ProcessedEvent, FormData } from '@/types';

export interface ChatInterfaceOptions {
  messages: Message[];
  isLoading: boolean;
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
  liveActivityEvents: ProcessedEvent[];
  historicalActivities: Record<string, ProcessedEvent[]>;
}

/**
 * Chat interface component - displays messages and input form
 */
export class ChatInterface extends BaseComponent {
  private options: ChatInterfaceOptions;
  private scrollArea!: HTMLElement;
  private messagesContainer!: HTMLElement;
  private inputForm!: InputForm;
  private messageBubbles: MessageBubble[] = [];
  private copiedMessageId: string | null = null;

  constructor(options: ChatInterfaceOptions) {
    super('div', 'chat-interface');
    this.options = options;
    this.createElements();
    this.renderMessages();
  }

  private createElements(): void {
    // Create scroll area
    this.scrollArea = createElement('div', {
      className: 'chat-interface__scroll-area'
    });

    // Create messages container
    this.messagesContainer = createElement('div', {
      className: 'chat-interface__messages'
    });

    this.scrollArea.appendChild(this.messagesContainer);

    // Create input form
    this.inputForm = new InputForm({
      onSubmit: this.options.onSubmit,
      onCancel: this.options.onCancel,
      isLoading: this.options.isLoading,
      hasHistory: this.options.messages.length > 0
    });

    // Append elements
    this._element.appendChild(this.scrollArea);
    this._element.appendChild(this.inputForm.render());
  }

  private renderMessages(): void {
    // Clear existing messages
    this.clearMessages();

    // Render each message
    this.options.messages.forEach((message, index) => {
      this.renderMessage(message, index);
    });

    // Add loading message if needed
    if (this.options.isLoading && this.shouldShowLoadingMessage()) {
      this.renderLoadingMessage();
    }

    // Scroll to bottom
    this.scrollToBottom();
  }

  private renderMessage(message: Message, index: number): void {
    const isLast = index === this.options.messages.length - 1;

    // Create message group container
    const messageGroup = createElement('div', {
      className: 'chat-interface__message-group'
    });

    // Create message row
    const messageRow = createElement('div', {
      className: `chat-interface__message-row ${message.type}`
    });

    // Create message bubble
    const messageBubble = new MessageBubble({
      message,
      historicalActivity: this.options.historicalActivities[message.id!],
      liveActivity: this.options.liveActivityEvents,
      isLastMessage: isLast,
      isOverallLoading: this.options.isLoading,
      onCopy: (messageId) => this.handleCopy(messageId),
      copiedMessageId: this.copiedMessageId
    });

    this.messageBubbles.push(messageBubble);
    messageRow.appendChild(messageBubble.render());
    messageGroup.appendChild(messageRow);
    this.messagesContainer.appendChild(messageGroup);
  }

  private renderLoadingMessage(): void {
    const loadingRow = createElement('div', {
      className: 'chat-interface__loading-message'
    });

    const loadingBubble = createElement('div', {
      className: 'loading-bubble'
    });

    if (this.options.liveActivityEvents.length > 0) {
      // Show activity timeline
      const activityTimeline = new ActivityTimeline({
        processedEvents: this.options.liveActivityEvents,
        isLoading: true
      });

      loadingBubble.appendChild(activityTimeline.render());
    } else {
      // Show simple loading indicator
      const loadingContent = createElement('div', {
        className: 'loading-content'
      });

      loadingContent.innerHTML = `
        <div class="spinner animate-spin">⟳</div>
        <span>Processing...</span>
      `;

      loadingBubble.appendChild(loadingContent);
    }

    loadingRow.appendChild(loadingBubble);
    this.messagesContainer.appendChild(loadingRow);
  }

  private shouldShowLoadingMessage(): boolean {
    const lastMessage = this.options.messages[this.options.messages.length - 1];
    return this.options.messages.length === 0 || (lastMessage && lastMessage.type === 'human');
  }

  private clearMessages(): void {
    // Destroy existing message bubbles
    this.messageBubbles.forEach(bubble => bubble.destroy());
    this.messageBubbles = [];

    // Clear messages container
    this.messagesContainer.innerHTML = '';
  }

  private handleCopy(messageId: string): void {
    this.copiedMessageId = messageId;
    
    // Update all message bubbles to reflect copy state
    this.messageBubbles.forEach(bubble => {
      bubble.updateOptions({ copiedMessageId: this.copiedMessageId });
    });

    // Reset copied state after 2 seconds
    setTimeout(() => {
      this.copiedMessageId = null;
      this.messageBubbles.forEach(bubble => {
        bubble.updateOptions({ copiedMessageId: null });
      });
    }, 2000);
  }

  private scrollToBottom(): void {
    // Use requestAnimationFrame to ensure DOM is updated
    requestAnimationFrame(() => {
      scrollToBottom(this.scrollArea);
    });
  }

  /**
   * Update the chat interface with new options
   */
  updateOptions(newOptions: Partial<ChatInterfaceOptions>): void {
    const oldMessagesLength = this.options.messages.length;
    this.options = { ...this.options, ...newOptions };

    // Update input form
    this.inputForm.updateOptions({
      onSubmit: this.options.onSubmit,
      onCancel: this.options.onCancel,
      isLoading: this.options.isLoading,
      hasHistory: this.options.messages.length > 0
    });

    // Re-render messages if they changed
    if (newOptions.messages || newOptions.isLoading !== undefined || newOptions.liveActivityEvents) {
      // Only re-render if messages actually changed or loading state changed
      const shouldRerender = 
        newOptions.messages !== undefined ||
        newOptions.isLoading !== undefined ||
        (newOptions.liveActivityEvents && this.options.isLoading);

      if (shouldRerender) {
        this.renderMessages();
      } else {
        // Just update existing message bubbles with new activity data
        this.messageBubbles.forEach((bubble, index) => {
          const isLast = index === this.options.messages.length - 1;
          bubble.updateOptions({
            liveActivity: this.options.liveActivityEvents,
            isLastMessage: isLast,
            isOverallLoading: this.options.isLoading
          });
        });
      }
    }

    // Scroll to bottom if new messages were added
    if (newOptions.messages && newOptions.messages.length > oldMessagesLength) {
      this.scrollToBottom();
    }
  }

  /**
   * Add a new message to the chat
   */
  addMessage(message: Message): void {
    this.options.messages.push(message);
    this.renderMessages();
  }

  /**
   * Update a specific message
   */
  updateMessage(messageId: string, updates: Partial<Message>): void {
    const messageIndex = this.options.messages.findIndex(m => m.id === messageId);
    if (messageIndex >= 0) {
      this.options.messages[messageIndex] = { ...this.options.messages[messageIndex], ...updates };
      this.renderMessages();
    }
  }

  /**
   * Clear all messages
   */
  clearAllMessages(): void {
    this.options.messages = [];
    this.options.historicalActivities = {};
    this.options.liveActivityEvents = [];
    this.renderMessages();
  }

  /**
   * Focus the input form
   */
  focus(): void {
    this.inputForm.focus();
  }

  /**
   * Get the scroll area element for external scroll control
   */
  getScrollArea(): HTMLElement {
    return this.scrollArea;
  }

  protected onDestroy(): void {
    this.clearMessages();
    if (this.inputForm) {
      this.inputForm.destroy();
    }
  }
}
