/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  height: 100%;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--neutral-100);
  background-color: var(--neutral-800);
  overflow-x: hidden;
}

#root {
  height: 100vh;
  width: 100%;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Remove default input styles */
input,
textarea,
select {
  font: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

/* Remove default list styles */
ul,
ol {
  list-style: none;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

a:focus {
  outline: 2px solid var(--blue-500);
  outline-offset: 2px;
}

/* Image defaults */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Form elements */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  padding: 0;
  margin: 0;
}

/* Remove default legend styles */
legend {
  padding: 0;
}

/* Table defaults */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

th,
td {
  text-align: left;
  vertical-align: top;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-800);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-600);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--neutral-600) var(--neutral-800);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--blue-500);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--blue-500);
  color: white;
}

::-moz-selection {
  background-color: var(--blue-500);
  color: white;
}
