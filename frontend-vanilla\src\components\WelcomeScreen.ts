import { BaseComponent } from './BaseComponent';
import { InputForm } from './InputForm';
import { createElement } from '@/utils/dom';
import type { FormData } from '@/types';

export interface WelcomeScreenOptions {
  onSubmit: (data: FormData) => void;
  onCancel: () => void;
  isLoading: boolean;
}

/**
 * Welcome screen component - displays the landing page with input form
 */
export class WelcomeScreen extends BaseComponent {
  private options: WelcomeScreenOptions;
  private inputForm!: InputForm;
  private headerElement!: HTMLElement;
  private formContainer!: HTMLElement;
  private footerElement!: HTMLElement;

  constructor(options: WelcomeScreenOptions) {
    super('div', 'welcome-screen');
    this.options = options;
    this.createElements();
  }

  private createElements(): void {
    // Create header section
    this.headerElement = createElement('div', {
      className: 'welcome-screen__header'
    });

    const title = createElement('h1', {
      textContent: 'Welcome.'
    });

    const subtitle = createElement('p', {
      textContent: 'How can I help you today?'
    });

    this.headerElement.appendChild(title);
    this.headerElement.appendChild(subtitle);

    // Create form container
    this.formContainer = createElement('div', {
      className: 'welcome-screen__form-container'
    });

    // Create input form
    this.inputForm = new InputForm({
      onSubmit: this.options.onSubmit,
      onCancel: this.options.onCancel,
      isLoading: this.options.isLoading,
      hasHistory: false
    });

    this.formContainer.appendChild(this.inputForm.render());

    // Create footer
    this.footerElement = createElement('p', {
      className: 'welcome-screen__footer',
      textContent: 'Powered by Google Gemini and LangChain LangGraph.'
    });

    // Append all elements
    this._element.appendChild(this.headerElement);
    this._element.appendChild(this.formContainer);
    this._element.appendChild(this.footerElement);
  }

  /**
   * Update loading state
   */
  setLoading(isLoading: boolean): void {
    this.options.isLoading = isLoading;
    this.inputForm.setLoading(isLoading);
  }

  /**
   * Update the welcome screen options
   */
  updateOptions(newOptions: Partial<WelcomeScreenOptions>): void {
    this.options = { ...this.options, ...newOptions };
    
    if (newOptions.isLoading !== undefined) {
      this.setLoading(newOptions.isLoading);
    }

    if (newOptions.onSubmit || newOptions.onCancel) {
      this.inputForm.updateOptions({
        onSubmit: this.options.onSubmit,
        onCancel: this.options.onCancel,
        isLoading: this.options.isLoading,
        hasHistory: false
      });
    }
  }

  /**
   * Focus the input form
   */
  focus(): void {
    this.inputForm.focus();
  }

  /**
   * Reset the form
   */
  reset(): void {
    this.inputForm.reset();
  }

  protected onDestroy(): void {
    if (this.inputForm) {
      this.inputForm.destroy();
    }
  }
}
