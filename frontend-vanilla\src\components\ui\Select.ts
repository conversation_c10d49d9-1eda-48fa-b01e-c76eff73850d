import { BaseComponent } from '../BaseComponent';
import { createElement } from '@/utils/dom';
import { cn } from '@/utils/helpers';

export interface SelectOption {
  value: string;
  label: string;
  icon?: string;
}

export interface SelectOptions {
  options: SelectOption[];
  value?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  onChange?: (value: string) => void;
}

/**
 * Select component
 */
export class Select extends BaseComponent {
  private options: SelectOptions;
  private isOpen = false;
  private selectedValue?: string;
  private triggerElement!: HTMLElement;
  private contentElement!: HTMLElement;
  private changeHandler?: (value: string) => void;

  constructor(options: SelectOptions) {
    super('div', cn('select', options.className));
    this.options = options;
    this.selectedValue = options.value;
    this.changeHandler = options.onChange;
    this.createSelectElements();
  }

  private createSelectElements(): void {
    // Create trigger element
    this.triggerElement = createElement('button', {
      className: 'select__trigger',
      attributes: {
        type: 'button',
        'aria-haspopup': 'listbox',
        'aria-expanded': 'false'
      }
    });

    // Create content element
    this.contentElement = createElement('div', {
      className: 'select__content hidden',
      attributes: {
        role: 'listbox'
      }
    });

    this._element.appendChild(this.triggerElement);
    this._element.appendChild(this.contentElement);

    this.updateTriggerContent();
    this.updateOptions();
  }

  private updateTriggerContent(): void {
    const selectedOption = this.options.options.find(opt => opt.value === this.selectedValue);
    const displayText = selectedOption?.label || this.options.placeholder || 'Select...';
    
    this.triggerElement.innerHTML = `
      <span class="select__trigger-text">${displayText}</span>
      <svg class="select__trigger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    `;
  }

  private updateOptions(): void {
    this.contentElement.innerHTML = '';
    
    this.options.options.forEach(option => {
      const optionElement = createElement('div', {
        className: cn(
          'select__item',
          option.value === this.selectedValue ? 'selected' : ''
        ),
        attributes: {
          'data-value': option.value,
          role: 'option',
          'aria-selected': option.value === this.selectedValue ? 'true' : 'false'
        }
      });

      if (option.icon) {
        optionElement.innerHTML = `
          <span class="select__item-icon">${option.icon}</span>
          <span class="select__item-label">${option.label}</span>
        `;
      } else {
        optionElement.textContent = option.label;
      }

      optionElement.addEventListener('click', () => {
        this.selectOption(option.value);
      });

      this.contentElement.appendChild(optionElement);
    });
  }

  private selectOption(value: string): void {
    const oldValue = this.selectedValue;
    this.selectedValue = value;
    
    this.updateTriggerContent();
    this.updateOptions();
    this.close();

    if (oldValue !== value && this.changeHandler) {
      this.changeHandler(value);
    }

    this.emit('change', { value, oldValue });
  }

  private open(): void {
    if (this.isOpen || this.options.disabled) return;
    
    this.isOpen = true;
    this.contentElement.classList.remove('hidden');
    this.triggerElement.setAttribute('aria-expanded', 'true');
    this.addClass('open');
    
    // Focus first option
    const firstOption = this.contentElement.querySelector('.select__item');
    if (firstOption instanceof HTMLElement) {
      firstOption.focus();
    }

    this.emit('open');
  }

  private close(): void {
    if (!this.isOpen) return;
    
    this.isOpen = false;
    this.contentElement.classList.add('hidden');
    this.triggerElement.setAttribute('aria-expanded', 'false');
    this.removeClass('open');
    
    this.emit('close');
  }

  private toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  private handleClickOutside = (event: MouseEvent): void => {
    if (!this._element.contains(event.target as Node)) {
      this.close();
    }
  };

  private handleKeyDown = (event: KeyboardEvent): void => {
    if (this.options.disabled) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        if (!this.isOpen) {
          event.preventDefault();
          this.open();
        }
        break;
      case 'Escape':
        if (this.isOpen) {
          event.preventDefault();
          this.close();
          this.triggerElement.focus();
        }
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!this.isOpen) {
          this.open();
        } else {
          this.focusNextOption();
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (!this.isOpen) {
          this.open();
        } else {
          this.focusPreviousOption();
        }
        break;
    }
  };

  private focusNextOption(): void {
    const options = Array.from(this.contentElement.querySelectorAll('.select__item'));
    const currentIndex = options.findIndex(opt => opt === document.activeElement);
    const nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
    (options[nextIndex] as HTMLElement).focus();
  }

  private focusPreviousOption(): void {
    const options = Array.from(this.contentElement.querySelectorAll('.select__item'));
    const currentIndex = options.findIndex(opt => opt === document.activeElement);
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
    (options[prevIndex] as HTMLElement).focus();
  }

  protected onMount(): void {
    this.triggerElement.addEventListener('click', () => this.toggle());
    this.triggerElement.addEventListener('keydown', this.handleKeyDown);
    document.addEventListener('click', this.handleClickOutside);
    
    if (this.options.disabled) {
      this.setDisabled(true);
    }
  }

  protected onUnmount(): void {
    this.triggerElement.removeEventListener('click', () => this.toggle());
    this.triggerElement.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('click', this.handleClickOutside);
  }

  /**
   * Get current value
   */
  getValue(): string | undefined {
    return this.selectedValue;
  }

  /**
   * Set value
   */
  setValue(value: string): void {
    if (this.options.options.some(opt => opt.value === value)) {
      this.selectOption(value);
    }
  }

  /**
   * Set disabled state
   */
  setDisabled(disabled: boolean): void {
    this.options.disabled = disabled;
    this.triggerElement.setAttribute('aria-disabled', disabled.toString());
    
    if (disabled) {
      this.addClass('disabled');
      this.close();
    } else {
      this.removeClass('disabled');
    }
  }

  /**
   * Update options
   */
  updateSelectOptions(newOptions: SelectOption[]): void {
    this.options.options = newOptions;
    this.updateOptions();
    
    // Reset selection if current value is no longer valid
    if (this.selectedValue && !newOptions.some(opt => opt.value === this.selectedValue)) {
      this.selectedValue = undefined;
      this.updateTriggerContent();
    }
  }

  /**
   * Set placeholder
   */
  setPlaceholder(placeholder: string): void {
    this.options.placeholder = placeholder;
    if (!this.selectedValue) {
      this.updateTriggerContent();
    }
  }
}
