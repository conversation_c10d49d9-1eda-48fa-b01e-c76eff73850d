// Message types from LangChain
export interface Message {
  id?: string;
  type: 'human' | 'ai';
  content: string | any;
}

// Activity timeline types
export interface ProcessedEvent {
  title: string;
  data: any;
}

// Chat configuration types
export interface ChatConfig {
  messages: Message[];
  initial_search_query_count: number;
  max_research_loops: number;
  reasoning_model: string;
}

// Component event types
export interface ComponentEvent {
  type: string;
  data?: any;
  target?: Component;
}

// Base component interface
export interface Component {
  element: HTMLElement;
  render(): HTMLElement;
  destroy(): void;
  on(event: string, callback: (data?: any) => void): void;
  emit(event: string, data?: any): void;
}

// Chat service types
export interface ChatStreamEvent {
  generate_query?: {
    query_list: string[];
  };
  web_research?: {
    sources_gathered: Array<{ label: string }>;
  };
  reflection?: {
    is_sufficient: boolean;
    follow_up_queries: string[];
  };
  finalize_answer?: any;
}

// Form data types
export interface FormData {
  inputValue: string;
  effort: 'low' | 'medium' | 'high';
  model: string;
}

// UI state types
export interface UIState {
  isLoading: boolean;
  messages: Message[];
  processedEvents: ProcessedEvent[];
  historicalActivities: Record<string, ProcessedEvent[]>;
  copiedMessageId: string | null;
}

// Event emitter types
export type EventCallback = (data?: any) => void;
export type EventMap = Record<string, EventCallback[]>;

// Router types
export interface Route {
  path: string;
  component: () => Component;
}

// API types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}
