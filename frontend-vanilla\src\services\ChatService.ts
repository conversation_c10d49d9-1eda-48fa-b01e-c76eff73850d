import { EventEmitter } from '@/utils/events';
import type { Message, ChatConfig, ProcessedEvent, ChatStreamEvent } from '@/types';

/**
 * Chat service that handles streaming communication with the backend
 * Mimics the functionality of the LangGraph SDK's useStream hook
 */
export class ChatService extends EventEmitter {
  private static instance: ChatService;
  private _isLoading = false;
  private _messages: Message[] = [];
  private currentController: AbortController | null = null;
  private apiUrl: string;

  constructor() {
    super();
    this.apiUrl = import.meta.env.DEV 
      ? "http://localhost:2024" 
      : "http://localhost:8123";
  }

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }

  get isLoading(): boolean {
    return this._isLoading;
  }

  get messages(): Message[] {
    return [...this._messages];
  }

  /**
   * Submit a new chat message and start streaming
   */
  async submit(config: ChatConfig): Promise<void> {
    if (this._isLoading) {
      console.warn('Chat service is already processing a request');
      return;
    }

    this._isLoading = true;
    this._messages = config.messages;
    this.currentController = new AbortController();

    this.emit('loadingStart');
    this.emit('messagesUpdate', this._messages);

    try {
      const response = await fetch(`${this.apiUrl}/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: config.messages,
          initial_search_query_count: config.initial_search_query_count,
          max_research_loops: config.max_research_loops,
          reasoning_model: config.reasoning_model,
        }),
        signal: this.currentController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      await this.processStream(response.body);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was cancelled');
        this.emit('cancelled');
      } else {
        console.error('Chat service error:', error);
        this.emit('error', error);
      }
    } finally {
      this._isLoading = false;
      this.currentController = null;
      this.emit('loadingEnd');
    }
  }

  /**
   * Stop the current streaming request
   */
  stop(): void {
    if (this.currentController) {
      this.currentController.abort();
      this.currentController = null;
    }
    this._isLoading = false;
    this.emit('loadingEnd');
    this.emit('stopped');
  }

  /**
   * Process the streaming response
   */
  private async processStream(body: ReadableStream<Uint8Array>): Promise<void> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        // Keep the last incomplete line in the buffer
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              this.processStreamLine(line);
            } catch (error) {
              console.error('Error processing stream line:', error, line);
            }
          }
        }
      }

      // Process any remaining data in the buffer
      if (buffer.trim()) {
        try {
          this.processStreamLine(buffer);
        } catch (error) {
          console.error('Error processing final buffer:', error, buffer);
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Process a single line from the stream
   */
  private processStreamLine(line: string): void {
    try {
      // Handle Server-Sent Events format
      if (line.startsWith('data: ')) {
        line = line.substring(6);
      }

      if (line === '[DONE]' || line === 'data: [DONE]') {
        this.emit('finished');
        return;
      }

      const data = JSON.parse(line);
      
      // Handle different types of streaming data
      if (data.type === 'message') {
        this.handleMessageUpdate(data);
      } else if (data.type === 'event') {
        this.handleEventUpdate(data);
      } else if (data.type === 'error') {
        this.emit('error', new Error(data.message || 'Unknown error'));
      } else {
        // Handle raw event data (similar to the React version)
        this.handleEventUpdate(data);
      }
    } catch (error) {
      console.error('Error parsing stream line:', error, line);
    }
  }

  /**
   * Handle message updates from the stream
   */
  private handleMessageUpdate(data: any): void {
    if (data.message) {
      const message: Message = {
        id: data.message.id || Date.now().toString(),
        type: data.message.type || 'ai',
        content: data.message.content || ''
      };

      // Update or add the message
      const existingIndex = this._messages.findIndex(m => m.id === message.id);
      if (existingIndex >= 0) {
        this._messages[existingIndex] = message;
      } else {
        this._messages.push(message);
      }

      this.emit('messagesUpdate', this._messages);
      this.emit('messageReceived', message);
    }
  }

  /**
   * Handle event updates from the stream (activity timeline events)
   */
  private handleEventUpdate(data: ChatStreamEvent): void {
    let processedEvent: ProcessedEvent | null = null;

    if (data.generate_query) {
      processedEvent = {
        title: "Generating Search Queries",
        data: data.generate_query.query_list.join(", "),
      };
    } else if (data.web_research) {
      const sources = data.web_research.sources_gathered || [];
      const numSources = sources.length;
      const uniqueLabels = [
        ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
      ];
      const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
      processedEvent = {
        title: "Web Research",
        data: `Gathered ${numSources} sources. Related to: ${
          exampleLabels || "N/A"
        }.`,
      };
    } else if (data.reflection) {
      processedEvent = {
        title: "Reflection",
        data: data.reflection.is_sufficient
          ? "Search successful, generating final answer."
          : `Need more information, searching for ${data.reflection.follow_up_queries.join(
              ", "
            )}`,
      };
    } else if (data.finalize_answer) {
      processedEvent = {
        title: "Finalizing Answer",
        data: "Composing and presenting the final answer.",
      };
      this.emit('finalizeEvent');
    }

    if (processedEvent) {
      this.emit('activityUpdate', processedEvent);
    }

    // Emit the raw event for any custom handling
    this.emit('streamEvent', data);
  }

  /**
   * Add a message to the chat
   */
  addMessage(message: Message): void {
    this._messages.push(message);
    this.emit('messagesUpdate', this._messages);
  }

  /**
   * Clear all messages
   */
  clearMessages(): void {
    this._messages = [];
    this.emit('messagesUpdate', this._messages);
  }

  /**
   * Get the last message
   */
  getLastMessage(): Message | null {
    return this._messages.length > 0 ? this._messages[this._messages.length - 1] : null;
  }

  /**
   * Update a specific message
   */
  updateMessage(id: string, updates: Partial<Message>): void {
    const index = this._messages.findIndex(m => m.id === id);
    if (index >= 0) {
      this._messages[index] = { ...this._messages[index], ...updates };
      this.emit('messagesUpdate', this._messages);
    }
  }
}
