// Font family utilities
.font-sans {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-serif {
  font-family: Georgia, Cambria, 'Times New Roman', Times, serif;
}

.font-mono {
  font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', monospace;
}

// Font size utilities
.text-xs { font-size: var(--text-xs); line-height: 1rem; }
.text-sm { font-size: var(--text-sm); line-height: 1.25rem; }
.text-base { font-size: var(--text-base); line-height: 1.5rem; }
.text-lg { font-size: var(--text-lg); line-height: 1.75rem; }
.text-xl { font-size: var(--text-xl); line-height: 1.75rem; }
.text-2xl { font-size: var(--text-2xl); line-height: 2rem; }
.text-3xl { font-size: var(--text-3xl); line-height: 2.25rem; }
.text-4xl { font-size: var(--text-4xl); line-height: 2.5rem; }
.text-5xl { font-size: var(--text-5xl); line-height: 1; }
.text-6xl { font-size: var(--text-6xl); line-height: 1; }

// Font weight utilities
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

// Line height utilities
.leading-none { line-height: var(--leading-none); }
.leading-tight { line-height: var(--leading-tight); }
.leading-snug { line-height: var(--leading-snug); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

// Text alignment utilities
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// Text color utilities
.text-neutral-50 { color: var(--neutral-50); }
.text-neutral-100 { color: var(--neutral-100); }
.text-neutral-200 { color: var(--neutral-200); }
.text-neutral-300 { color: var(--neutral-300); }
.text-neutral-400 { color: var(--neutral-400); }
.text-neutral-500 { color: var(--neutral-500); }
.text-neutral-600 { color: var(--neutral-600); }
.text-neutral-700 { color: var(--neutral-700); }
.text-neutral-800 { color: var(--neutral-800); }
.text-neutral-900 { color: var(--neutral-900); }

.text-blue-400 { color: var(--blue-400); }
.text-blue-500 { color: var(--blue-500); }
.text-blue-600 { color: var(--blue-600); }

.text-red-400 { color: var(--red-400); }
.text-red-500 { color: var(--red-500); }
.text-red-600 { color: var(--red-600); }

.text-yellow-400 { color: var(--yellow-400); }
.text-yellow-500 { color: var(--yellow-500); }

.text-orange-400 { color: var(--orange-400); }
.text-orange-500 { color: var(--orange-500); }

.text-purple-400 { color: var(--purple-400); }
.text-purple-500 { color: var(--purple-500); }

// Text decoration utilities
.underline { text-decoration: underline; }
.overline { text-decoration: overline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

// Text transform utilities
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// Text overflow utilities
.truncate {
  @include truncate;
}

.text-ellipsis { text-overflow: ellipsis; }
.text-clip { text-overflow: clip; }

// Whitespace utilities
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

// Word break utilities
.break-normal {
  overflow-wrap: normal;
  word-break: normal;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

// Letter spacing utilities
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

// Responsive typography
@include respond-to('md') {
  .md\:text-sm { font-size: var(--text-sm); line-height: 1.25rem; }
  .md\:text-base { font-size: var(--text-base); line-height: 1.5rem; }
  .md\:text-lg { font-size: var(--text-lg); line-height: 1.75rem; }
  .md\:text-xl { font-size: var(--text-xl); line-height: 1.75rem; }
  .md\:text-2xl { font-size: var(--text-2xl); line-height: 2rem; }
  .md\:text-3xl { font-size: var(--text-3xl); line-height: 2.25rem; }
  .md\:text-4xl { font-size: var(--text-4xl); line-height: 2.5rem; }
  .md\:text-5xl { font-size: var(--text-5xl); line-height: 1; }
  .md\:text-6xl { font-size: var(--text-6xl); line-height: 1; }
}

// Placeholder text color
.placeholder-neutral-300::placeholder { color: var(--neutral-300); }
.placeholder-neutral-400::placeholder { color: var(--neutral-400); }
.placeholder-neutral-500::placeholder { color: var(--neutral-500); }
.placeholder-neutral-600::placeholder { color: var(--neutral-600); }
