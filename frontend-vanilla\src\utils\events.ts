import type { EventCallback, EventMap } from '@/types';

/**
 * Simple event emitter for component communication
 */
export class EventEmitter {
  private events: EventMap = {};

  on(event: string, callback: EventCallback): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  off(event: string, callback: EventCallback): void {
    if (!this.events[event]) return;
    
    const index = this.events[event].indexOf(callback);
    if (index > -1) {
      this.events[event].splice(index, 1);
    }
  }

  emit(event: string, data?: any): void {
    if (!this.events[event]) return;
    
    this.events[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event callback for ${event}:`, error);
      }
    });
  }

  once(event: string, callback: EventCallback): void {
    const onceCallback = (data?: any) => {
      callback(data);
      this.off(event, onceCallback);
    };
    this.on(event, onceCallback);
  }

  removeAllListeners(event?: string): void {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

/**
 * Global event bus for application-wide communication
 */
export const eventBus = new EventEmitter();

/**
 * Custom event types for the application
 */
export const Events = {
  // Chat events
  CHAT_MESSAGE_SENT: 'chat:message:sent',
  CHAT_MESSAGE_RECEIVED: 'chat:message:received',
  CHAT_LOADING_START: 'chat:loading:start',
  CHAT_LOADING_END: 'chat:loading:end',
  CHAT_ERROR: 'chat:error',
  CHAT_CANCEL: 'chat:cancel',
  
  // Activity events
  ACTIVITY_UPDATE: 'activity:update',
  ACTIVITY_COMPLETE: 'activity:complete',
  
  // UI events
  UI_STATE_CHANGE: 'ui:state:change',
  UI_COPY_SUCCESS: 'ui:copy:success',
  UI_SCROLL_TO_BOTTOM: 'ui:scroll:bottom',
  
  // Navigation events
  ROUTE_CHANGE: 'route:change',
  
  // Form events
  FORM_SUBMIT: 'form:submit',
  FORM_RESET: 'form:reset',
} as const;
