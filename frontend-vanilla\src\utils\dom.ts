/**
 * DOM utility functions for element creation and manipulation
 */

export function createElement<K extends keyof HTMLElementTagNameMap>(
  tag: K,
  options: {
    className?: string;
    id?: string;
    textContent?: string;
    innerHTML?: string;
    attributes?: Record<string, string>;
    dataset?: Record<string, string>;
    styles?: Partial<CSSStyleDeclaration>;
  } = {}
): HTMLElementTagNameMap[K] {
  const element = document.createElement(tag);
  
  if (options.className) {
    element.className = options.className;
  }
  
  if (options.id) {
    element.id = options.id;
  }
  
  if (options.textContent) {
    element.textContent = options.textContent;
  }
  
  if (options.innerHTML) {
    element.innerHTML = options.innerHTML;
  }
  
  if (options.attributes) {
    Object.entries(options.attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
  }
  
  if (options.dataset) {
    Object.entries(options.dataset).forEach(([key, value]) => {
      element.dataset[key] = value;
    });
  }
  
  if (options.styles) {
    Object.assign(element.style, options.styles);
  }
  
  return element;
}

export function query<T extends Element = Element>(selector: string, parent: Element | Document = document): T | null {
  return parent.querySelector<T>(selector);
}

export function queryAll<T extends Element = Element>(selector: string, parent: Element | Document = document): NodeListOf<T> {
  return parent.querySelectorAll<T>(selector);
}

export function addClass(element: Element, ...classes: string[]): void {
  element.classList.add(...classes);
}

export function removeClass(element: Element, ...classes: string[]): void {
  element.classList.remove(...classes);
}

export function toggleClass(element: Element, className: string, force?: boolean): boolean {
  return element.classList.toggle(className, force);
}

export function hasClass(element: Element, className: string): boolean {
  return element.classList.contains(className);
}

export function setAttributes(element: Element, attributes: Record<string, string>): void {
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
}

export function removeAttributes(element: Element, ...attributes: string[]): void {
  attributes.forEach(attr => element.removeAttribute(attr));
}

export function empty(element: Element): void {
  while (element.firstChild) {
    element.removeChild(element.firstChild);
  }
}

export function append(parent: Element, ...children: (Element | string)[]): void {
  children.forEach(child => {
    if (typeof child === 'string') {
      parent.appendChild(document.createTextNode(child));
    } else {
      parent.appendChild(child);
    }
  });
}

export function prepend(parent: Element, ...children: (Element | string)[]): void {
  children.reverse().forEach(child => {
    if (typeof child === 'string') {
      parent.insertBefore(document.createTextNode(child), parent.firstChild);
    } else {
      parent.insertBefore(child, parent.firstChild);
    }
  });
}

export function insertAfter(newElement: Element, referenceElement: Element): void {
  referenceElement.parentNode?.insertBefore(newElement, referenceElement.nextSibling);
}

export function insertBefore(newElement: Element, referenceElement: Element): void {
  referenceElement.parentNode?.insertBefore(newElement, referenceElement);
}

export function remove(element: Element): void {
  element.parentNode?.removeChild(element);
}

export function scrollToBottom(element: Element): void {
  element.scrollTop = element.scrollHeight;
}

export function isElementInViewport(element: Element): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
