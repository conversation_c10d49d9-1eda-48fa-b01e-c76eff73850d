import { BaseComponent } from './BaseComponent';
import { ActivityTimeline } from './ActivityTimeline';
import { Button } from './ui/Button';
import { MarkdownRenderer } from '@/services/MarkdownRenderer';
import { createElement } from '@/utils/dom';
import { copyToClipboard } from '@/utils/helpers';
import type { Message, ProcessedEvent } from '@/types';

export interface MessageBubbleOptions {
  message: Message;
  historicalActivity?: ProcessedEvent[];
  liveActivity?: ProcessedEvent[];
  isLastMessage: boolean;
  isOverallLoading: boolean;
  onCopy?: (messageId: string) => void;
  copiedMessageId?: string | null;
}

/**
 * Message bubble component for displaying chat messages
 */
export class MessageBubble extends BaseComponent {
  private options: MessageBubbleOptions;
  private activityTimeline?: ActivityTimeline;
  private copyButton?: Button;
  private markdownRenderer: MarkdownRenderer;
  private contentElement!: HTMLElement;
  private activitySection?: HTMLElement;

  constructor(options: MessageBubbleOptions) {
    super('div', `message-bubble ${options.message.type}`);
    this.options = options;
    this.markdownRenderer = MarkdownRenderer.getInstance();
    this.createElements();
  }

  private createElements(): void {
    if (this.options.message.type === 'human') {
      this.createHumanMessage();
    } else {
      this.createAiMessage();
    }
  }

  private createHumanMessage(): void {
    this.contentElement = createElement('div', {
      className: 'markdown-content'
    });

    const content = typeof this.options.message.content === 'string' 
      ? this.options.message.content 
      : JSON.stringify(this.options.message.content);

    this.contentElement.innerHTML = this.markdownRenderer.render(content);
    this._element.appendChild(this.contentElement);
  }

  private createAiMessage(): void {
    // Determine which activity events to show
    const activityForThisBubble = this.options.isLastMessage && this.options.isOverallLoading 
      ? this.options.liveActivity 
      : this.options.historicalActivity;
    
    const isLiveActivityForThisBubble = this.options.isLastMessage && this.options.isOverallLoading;

    // Create activity section if there are events
    if (activityForThisBubble && activityForThisBubble.length > 0) {
      this.activitySection = createElement('div', {
        className: 'activity-section'
      });

      this.activityTimeline = new ActivityTimeline({
        processedEvents: activityForThisBubble,
        isLoading: isLiveActivityForThisBubble
      });

      this.activitySection.appendChild(this.activityTimeline.render());
      this._element.appendChild(this.activitySection);
    }

    // Create content section
    this.contentElement = createElement('div', {
      className: 'markdown-content'
    });

    const content = typeof this.options.message.content === 'string' 
      ? this.options.message.content 
      : JSON.stringify(this.options.message.content);

    this.contentElement.innerHTML = this.markdownRenderer.render(content);
    this._element.appendChild(this.contentElement);

    // Create copy button
    this.createCopyButton();
  }

  private createCopyButton(): void {
    const isCopied = this.options.copiedMessageId === this.options.message.id;
    
    this.copyButton = new Button({
      variant: 'secondary',
      className: 'copy-button',
      children: [
        createElement('span', { textContent: isCopied ? 'Copied' : 'Copy' }),
        createElement('span', { 
          innerHTML: isCopied ? this.getIconSvg('copy-check') : this.getIconSvg('copy')
        })
      ],
      onClick: () => this.handleCopy()
    });

    this._element.appendChild(this.copyButton.render());
  }

  private async handleCopy(): Promise<void> {
    const content = typeof this.options.message.content === 'string' 
      ? this.options.message.content 
      : JSON.stringify(this.options.message.content);

    const success = await copyToClipboard(content);
    
    if (success && this.options.onCopy && this.options.message.id) {
      this.options.onCopy(this.options.message.id);
      
      // Update button text temporarily
      if (this.copyButton) {
        this.copyButton.setContent([
          createElement('span', { textContent: 'Copied' }),
          createElement('span', { innerHTML: this.getIconSvg('copy-check') })
        ]);
        
        // Reset after 2 seconds
        setTimeout(() => {
          if (this.copyButton && !this._destroyed) {
            this.copyButton.setContent([
              createElement('span', { textContent: 'Copy' }),
              createElement('span', { innerHTML: this.getIconSvg('copy') })
            ]);
          }
        }, 2000);
      }
    }
  }

  private getIconSvg(name: string): string {
    const icons: Record<string, string> = {
      'copy': `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path></svg>`,
      'copy-check': `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path><path d="M9 15l2 2 4-4"></path></svg>`
    };
    return icons[name] || '';
  }

  /**
   * Update the message bubble with new options
   */
  updateOptions(newOptions: Partial<MessageBubbleOptions>): void {
    this.options = { ...this.options, ...newOptions };

    // Update activity timeline if it exists
    if (this.activityTimeline && newOptions.liveActivity !== undefined) {
      const activityForThisBubble = this.options.isLastMessage && this.options.isOverallLoading 
        ? this.options.liveActivity 
        : this.options.historicalActivity;
      
      const isLiveActivityForThisBubble = this.options.isLastMessage && this.options.isOverallLoading;

      if (activityForThisBubble) {
        this.activityTimeline.updateEvents(activityForThisBubble, isLiveActivityForThisBubble);
      }
    }

    // Update copy button if copied state changed
    if (this.copyButton && newOptions.copiedMessageId !== undefined) {
      const isCopied = this.options.copiedMessageId === this.options.message.id;
      this.copyButton.setContent([
        createElement('span', { textContent: isCopied ? 'Copied' : 'Copy' }),
        createElement('span', { 
          innerHTML: isCopied ? this.getIconSvg('copy-check') : this.getIconSvg('copy')
        })
      ]);
    }

    // Update content if message changed
    if (newOptions.message && newOptions.message.content !== this.options.message.content) {
      const content = typeof this.options.message.content === 'string' 
        ? this.options.message.content 
        : JSON.stringify(this.options.message.content);

      this.contentElement.innerHTML = this.markdownRenderer.render(content);
    }
  }

  /**
   * Get the message associated with this bubble
   */
  getMessage(): Message {
    return this.options.message;
  }

  /**
   * Check if this is the last message
   */
  isLastMessage(): boolean {
    return this.options.isLastMessage;
  }

  /**
   * Check if this message is currently loading
   */
  isLoading(): boolean {
    return this.options.isLastMessage && this.options.isOverallLoading;
  }

  protected onDestroy(): void {
    if (this.activityTimeline) {
      this.activityTimeline.destroy();
    }
    if (this.copyButton) {
      this.copyButton.destroy();
    }
  }
}
