// Chat interface styles
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__scroll-area {
    flex-grow: 1;
    overflow-y: auto;
    max-height: 100%;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: var(--neutral-800);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--neutral-600);
      border-radius: var(--radius-full);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--neutral-500);
    }
  }

  &__messages {
    padding: var(--spacing-4) var(--spacing-6);
    padding-top: var(--spacing-16);
    max-width: 64rem; // max-w-4xl
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);

    @include respond-to('md') {
      padding: var(--spacing-6);
      padding-top: var(--spacing-16);
    }
  }

  &__message-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  &__message-row {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);

    &.human {
      justify-content: flex-end;
    }

    &.ai {
      justify-content: flex-start;
    }
  }

  &__loading-message {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    margin-top: var(--spacing-3);

    .loading-bubble {
      position: relative;
      background-color: var(--neutral-800);
      color: var(--neutral-100);
      border-radius: var(--radius-xl);
      border-bottom-left-radius: 0;
      padding: var(--spacing-3);
      box-shadow: var(--shadow-sm);
      word-wrap: break-word;
      width: 100%;
      min-height: 56px;
      max-width: 85%;

      @include respond-to('md') {
        max-width: 80%;
      }

      .loading-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 100%;

        .spinner {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: var(--spacing-2);
          color: var(--neutral-400);
          @include loading-spinner;
        }

        span {
          color: var(--neutral-300);
        }
      }

      .activity-timeline {
        font-size: var(--text-xs);
      }
    }
  }
}
