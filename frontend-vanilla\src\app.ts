import { BaseComponent } from './components/BaseComponent';
import { WelcomeScreen } from './components/WelcomeScreen';
import { ChatInterface } from './components/ChatInterface';
import { ChatService } from './services/ChatService';
import { createElement } from './utils/dom';
import { generateId } from './utils/helpers';
import { Events, eventBus } from './utils/events';
import type { Message, ProcessedEvent, FormData, UIState } from './types';

/**
 * Main application component
 */
export class App extends BaseComponent {
  private chatService: ChatService;
  private welcomeScreen?: WelcomeScreen;
  private chatInterface?: ChatInterface;
  private mainContent!: HTMLElement;
  private contentArea!: HTMLElement;

  // Application state
  private state: UIState = {
    isLoading: false,
    messages: [],
    processedEvents: [],
    historicalActivities: {},
    copiedMessageId: null
  };

  private hasFinalizeEventOccurred = false;

  constructor() {
    super('div', 'app');
    this.chatService = ChatService.getInstance();
    this.createElements();
    this.setupEventListeners();
    this.renderCurrentView();
  }

  private createElements(): void {
    // Create main content container
    this.mainContent = createElement('main', {
      className: 'main-content'
    });

    // Create content area
    this.contentArea = createElement('div', {
      className: 'content-area'
    });

    this.mainContent.appendChild(this.contentArea);
    this._element.appendChild(this.mainContent);
  }

  private setupEventListeners(): void {
    // Chat service events
    this.chatService.on('loadingStart', () => {
      this.setState({ isLoading: true });
    });

    this.chatService.on('loadingEnd', () => {
      this.setState({ isLoading: false });
    });

    this.chatService.on('messagesUpdate', (messages: Message[]) => {
      this.setState({ messages });
    });

    this.chatService.on('activityUpdate', (event: ProcessedEvent) => {
      this.setState({
        processedEvents: [...this.state.processedEvents, event]
      });
    });

    this.chatService.on('finalizeEvent', () => {
      this.hasFinalizeEventOccurred = true;
    });

    this.chatService.on('finished', () => {
      this.handleChatFinished();
    });

    this.chatService.on('error', (error: Error) => {
      console.error('Chat service error:', error);
      this.setState({ isLoading: false });
    });

    // Global event bus events
    eventBus.on(Events.UI_SCROLL_TO_BOTTOM, () => {
      if (this.chatInterface) {
        const scrollArea = this.chatInterface.getScrollArea();
        scrollArea.scrollTop = scrollArea.scrollHeight;
      }
    });
  }

  private setState(newState: Partial<UIState>): void {
    this.state = { ...this.state, ...newState };
    this.updateViews();
    eventBus.emit(Events.UI_STATE_CHANGE, this.state);
  }

  private updateViews(): void {
    // Update welcome screen if it exists
    if (this.welcomeScreen) {
      this.welcomeScreen.updateOptions({
        isLoading: this.state.isLoading,
        onSubmit: (data) => this.handleSubmit(data),
        onCancel: () => this.handleCancel()
      });
    }

    // Update chat interface if it exists
    if (this.chatInterface) {
      this.chatInterface.updateOptions({
        messages: this.state.messages,
        isLoading: this.state.isLoading,
        liveActivityEvents: this.state.processedEvents,
        historicalActivities: this.state.historicalActivities,
        onSubmit: (data) => this.handleSubmit(data),
        onCancel: () => this.handleCancel()
      });
    }

    // Trigger scroll to bottom when messages update
    if (this.state.messages.length > 0) {
      setTimeout(() => {
        eventBus.emit(Events.UI_SCROLL_TO_BOTTOM);
      }, 100);
    }
  }

  private renderCurrentView(): void {
    // Clear content area
    this.contentArea.innerHTML = '';
    
    // Destroy existing components
    if (this.welcomeScreen) {
      this.welcomeScreen.destroy();
      this.welcomeScreen = undefined;
    }
    if (this.chatInterface) {
      this.chatInterface.destroy();
      this.chatInterface = undefined;
    }

    // Update content area class
    if (this.state.messages.length === 0) {
      this.contentArea.className = 'content-area welcome-mode';
      this.renderWelcomeScreen();
    } else {
      this.contentArea.className = 'content-area';
      this.renderChatInterface();
    }
  }

  private renderWelcomeScreen(): void {
    this.welcomeScreen = new WelcomeScreen({
      onSubmit: (data) => this.handleSubmit(data),
      onCancel: () => this.handleCancel(),
      isLoading: this.state.isLoading
    });

    this.contentArea.appendChild(this.welcomeScreen.render());
  }

  private renderChatInterface(): void {
    this.chatInterface = new ChatInterface({
      messages: this.state.messages,
      isLoading: this.state.isLoading,
      liveActivityEvents: this.state.processedEvents,
      historicalActivities: this.state.historicalActivities,
      onSubmit: (data) => this.handleSubmit(data),
      onCancel: () => this.handleCancel()
    });

    this.contentArea.appendChild(this.chatInterface.render());
  }

  private handleSubmit(data: FormData): void {
    if (!data.inputValue.trim() || this.state.isLoading) return;

    // Reset processed events for new conversation
    this.setState({ processedEvents: [] });
    this.hasFinalizeEventOccurred = false;

    // Convert effort to search parameters
    let initial_search_query_count = 0;
    let max_research_loops = 0;
    
    switch (data.effort) {
      case 'low':
        initial_search_query_count = 1;
        max_research_loops = 1;
        break;
      case 'medium':
        initial_search_query_count = 3;
        max_research_loops = 3;
        break;
      case 'high':
        initial_search_query_count = 5;
        max_research_loops = 10;
        break;
    }

    // Create new human message
    const newMessage: Message = {
      type: 'human',
      content: data.inputValue,
      id: generateId('msg')
    };

    const newMessages = [...this.state.messages, newMessage];

    // Submit to chat service
    this.chatService.submit({
      messages: newMessages,
      initial_search_query_count,
      max_research_loops,
      reasoning_model: data.model
    });

    // If this is the first message, switch to chat interface
    if (this.state.messages.length === 0) {
      this.renderCurrentView();
    }

    eventBus.emit(Events.FORM_SUBMIT, data);
  }

  private handleCancel(): void {
    this.chatService.stop();
    
    // Reload the page to reset state
    setTimeout(() => {
      window.location.reload();
    }, 100);

    eventBus.emit(Events.CHAT_CANCEL);
  }

  private handleChatFinished(): void {
    // Store historical activities when chat finishes
    if (this.hasFinalizeEventOccurred && this.state.messages.length > 0) {
      const lastMessage = this.state.messages[this.state.messages.length - 1];
      if (lastMessage && lastMessage.type === 'ai' && lastMessage.id) {
        this.setState({
          historicalActivities: {
            ...this.state.historicalActivities,
            [lastMessage.id]: [...this.state.processedEvents]
          }
        });
      }
      this.hasFinalizeEventOccurred = false;
    }

    eventBus.emit(Events.CHAT_LOADING_END);
  }

  /**
   * Get current application state
   */
  getState(): UIState {
    return { ...this.state };
  }

  /**
   * Reset the application to initial state
   */
  reset(): void {
    this.chatService.clearMessages();
    this.setState({
      isLoading: false,
      messages: [],
      processedEvents: [],
      historicalActivities: {},
      copiedMessageId: null
    });
    this.hasFinalizeEventOccurred = false;
    this.renderCurrentView();
  }

  /**
   * Check if the app is currently loading
   */
  isLoading(): boolean {
    return this.state.isLoading;
  }

  /**
   * Get current messages
   */
  getMessages(): Message[] {
    return [...this.state.messages];
  }

  protected onDestroy(): void {
    if (this.welcomeScreen) {
      this.welcomeScreen.destroy();
    }
    if (this.chatInterface) {
      this.chatInterface.destroy();
    }
    
    // Clean up chat service listeners
    this.chatService.removeAllListeners();
  }
}
