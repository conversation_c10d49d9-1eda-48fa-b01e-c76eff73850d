import { marked } from 'marked';
import { createElement } from '@/utils/dom';

/**
 * Markdown renderer service that converts markdown to HTML
 * with custom styling to match the React version
 */
export class MarkdownRenderer {
  private static instance: MarkdownRenderer;
  private renderer: marked.Renderer;

  constructor() {
    this.renderer = new marked.Renderer();
    this.setupCustomRenderer();
    this.configureMarked();
  }

  static getInstance(): MarkdownRenderer {
    if (!MarkdownRenderer.instance) {
      MarkdownRenderer.instance = new MarkdownRenderer();
    }
    return MarkdownRenderer.instance;
  }

  private setupCustomRenderer(): void {
    // Custom heading renderers
    this.renderer.heading = (text: string, level: number): string => {
      const classes = {
        1: 'text-2xl font-bold mt-4 mb-2',
        2: 'text-xl font-bold mt-3 mb-2',
        3: 'text-lg font-bold mt-3 mb-1',
        4: 'text-base font-bold mt-2 mb-1',
        5: 'text-sm font-bold mt-2 mb-1',
        6: 'text-xs font-bold mt-2 mb-1'
      };
      
      return `<h${level} class="${classes[level as keyof typeof classes] || classes[6]}">${text}</h${level}>`;
    };

    // Custom paragraph renderer
    this.renderer.paragraph = (text: string): string => {
      return `<p class="mb-3 leading-7">${text}</p>`;
    };

    // Custom link renderer with badge styling
    this.renderer.link = (href: string, title: string | null, text: string): string => {
      const titleAttr = title ? ` title="${title}"` : '';
      return `<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-neutral-700 text-blue-400 mx-0.5">
        <a href="${href}" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300 text-xs"${titleAttr}>${text}</a>
      </span>`;
    };

    // Custom list renderers
    this.renderer.list = (body: string, ordered: boolean): string => {
      const tag = ordered ? 'ol' : 'ul';
      const listClass = ordered ? 'list-decimal pl-6 mb-3' : 'list-disc pl-6 mb-3';
      return `<${tag} class="${listClass}">${body}</${tag}>`;
    };

    this.renderer.listitem = (text: string): string => {
      return `<li class="mb-1">${text}</li>`;
    };

    // Custom blockquote renderer
    this.renderer.blockquote = (quote: string): string => {
      return `<blockquote class="border-l-4 border-neutral-600 pl-4 italic my-3 text-sm">${quote}</blockquote>`;
    };

    // Custom code renderers
    this.renderer.code = (code: string, language?: string): string => {
      const langClass = language ? ` language-${language}` : '';
      return `<pre class="bg-neutral-900 p-3 rounded-lg overflow-x-auto font-mono text-xs my-3"><code class="font-mono text-xs${langClass}">${this.escapeHtml(code)}</code></pre>`;
    };

    this.renderer.codespan = (code: string): string => {
      return `<code class="bg-neutral-900 rounded px-1 py-0.5 font-mono text-xs">${this.escapeHtml(code)}</code>`;
    };

    // Custom horizontal rule renderer
    this.renderer.hr = (): string => {
      return `<hr class="border-neutral-600 my-4">`;
    };

    // Custom table renderers
    this.renderer.table = (header: string, body: string): string => {
      return `<div class="my-3 overflow-x-auto">
        <table class="border-collapse w-full">
          <thead>${header}</thead>
          <tbody>${body}</tbody>
        </table>
      </div>`;
    };

    this.renderer.tablerow = (content: string): string => {
      return `<tr>${content}</tr>`;
    };

    this.renderer.tablecell = (content: string, flags: { header: boolean; align: string | null }): string => {
      const tag = flags.header ? 'th' : 'td';
      const alignClass = flags.align ? ` text-${flags.align}` : '';
      const baseClass = flags.header 
        ? 'border border-neutral-600 px-3 py-2 text-left font-bold'
        : 'border border-neutral-600 px-3 py-2';
      
      return `<${tag} class="${baseClass}${alignClass}">${content}</${tag}>`;
    };

    // Custom image renderer
    this.renderer.image = (href: string, title: string | null, text: string): string => {
      const titleAttr = title ? ` title="${title}"` : '';
      const altAttr = text ? ` alt="${text}"` : '';
      return `<img src="${href}" class="max-w-full h-auto rounded-lg my-3"${titleAttr}${altAttr}>`;
    };

    // Custom strong and em renderers
    this.renderer.strong = (text: string): string => {
      return `<strong class="font-semibold">${text}</strong>`;
    };

    this.renderer.em = (text: string): string => {
      return `<em class="italic">${text}</em>`;
    };
  }

  private configureMarked(): void {
    marked.setOptions({
      renderer: this.renderer,
      gfm: true,
      breaks: true,
      pedantic: false,
      sanitize: false,
      smartLists: true,
      smartypants: false
    });
  }

  /**
   * Render markdown string to HTML
   */
  render(markdown: string): string {
    try {
      return marked(markdown);
    } catch (error) {
      console.error('Error rendering markdown:', error);
      return this.escapeHtml(markdown);
    }
  }

  /**
   * Render markdown and create DOM element
   */
  renderToElement(markdown: string, className?: string): HTMLElement {
    const html = this.render(markdown);
    const element = createElement('div', {
      className,
      innerHTML: html
    });
    
    // Post-process links to ensure they open in new tabs
    const links = element.querySelectorAll('a');
    links.forEach(link => {
      if (!link.getAttribute('target')) {
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');
      }
    });

    return element;
  }

  /**
   * Escape HTML to prevent XSS
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Strip markdown and return plain text
   */
  stripMarkdown(markdown: string): string {
    // Simple markdown stripping - remove common markdown syntax
    return markdown
      .replace(/#{1,6}\s+/g, '') // Headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
      .replace(/\*(.*?)\*/g, '$1') // Italic
      .replace(/`(.*?)`/g, '$1') // Inline code
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Links
      .replace(/!\[(.*?)\]\(.*?\)/g, '$1') // Images
      .replace(/>\s+/g, '') // Blockquotes
      .replace(/^\s*[-*+]\s+/gm, '') // Unordered lists
      .replace(/^\s*\d+\.\s+/gm, '') // Ordered lists
      .trim();
  }
}
